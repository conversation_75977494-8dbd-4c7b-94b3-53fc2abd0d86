# 阿里云人机验证SDK接入计划（Web端短信验证）

## 项目背景

当前uniapp vue2项目是一个多端金融H5应用，支持微信小程序、H5、微信公众号等多端部署。项目中已有完善的短信验证码发送机制，现需要在web端发送短信验证码前增加阿里云人机验证功能。

## 需求分析

- **目标**：在web端发送短信验证码前增加阿里云人机验证
- **范围**：仅处理H5/web端，其他平台不考虑
- **场景**：用户点击"获取验证码"按钮时，先进行人机验证，验证通过后再发送短信

## 技术方案

### 1. SDK引入和基础配置

#### 1.1 修改index.html
在项目根目录的`index.html`文件中引入阿里云验证码SDK：

```html
<!-- 在<head>标签中添加 -->
<script>
  window.AliyunCaptchaConfig = {
    region: 'cn',
    prefix: 'your-prefix', // 需要配置实际的前缀
  };
</script>
<script type="text/javascript" src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js"></script>
```

#### 1.2 创建验证码配置文件
位置：`utils/captcha-config.js`

```javascript
// 验证码配置
export const captchaConfig = {
  // 开发环境
  development: {
    sceneId: 'dev-scene-id',
    prefix: 'dev-prefix'
  },
  // 测试环境
  test: {
    sceneId: 'test-scene-id', 
    prefix: 'test-prefix'
  },
  // 生产环境
  production: {
    sceneId: 'prod-scene-id',
    prefix: 'prod-prefix'
  }
}

// 获取当前环境配置
export const getCurrentCaptchaConfig = () => {
  const env = process.env.NODE_ENV || 'development'
  return captchaConfig[env] || captchaConfig.development
}
```

### 2. 组件开发

#### 2.1 基础验证码组件
位置：`components/captcha/aliyun-captcha.vue`

功能：
- 封装阿里云验证码SDK
- 提供统一的调用接口
- 支持弹窗模式
- 处理验证成功/失败回调

#### 2.2 短信验证码组件
位置：`components/captcha/sms-captcha.vue`

功能：
- 集成人机验证+短信发送的完整流程
- 替换现有的"获取验证码"按钮
- 保持原有UI样式和交互逻辑
- 支持倒计时功能

### 3. API接口扩展

#### 3.1 新增验证码相关API
位置：`apis/captcha.js`

```javascript
import { $post } from '@/utils/request'

// 验证码验签接口
export const verifyCaptcha = (data = {}) => {
  return $post({
    url: '/captcha/verify',
    data,
    isEncrypt: false,
    deEncrypt: false
  })
}
```

#### 3.2 改造现有短信发送接口
修改`apis/common-2.js`中的`sendSmsCode`接口，增加验证码验签参数：

```javascript
// 注册，发送短信验证码(加验证码验签版)
export const sendSmsCodeWithCaptcha = (data = {}) => {
  const phone = String(data.phone || '')
  const channelId = String(data.channelId || '')
  const timestamp = String(Date.now())
  const sign = md5(phone + channelId + timestamp)

  const finalData = {
    ...data,
    timestamp,
    // 新增验证码验签参数
    captchaVerifyParam: data.captchaVerifyParam
  }

  return $post({
    url: `/user/sendV2WithCaptcha`,
    isEncrypt: true,
    deEncrypt: false,
    data: finalData,
    header: {
      sign
    }
  })
}
```

### 4. 业务页面集成

#### 4.1 需要改造的页面识别

基于代码分析，以下页面需要集成人机验证：

**首页模板组件**：
- `components/template/v24.vue`
- `components/template/v7.vue`
- `components/template/v152.vue`
- `components/template/v86.vue`
- `components/template/v55.vue`
- `components/template/v149.vue`
- `components/template/v15.vue`
- `components/template/v52.vue`
- `components/template/v97.vue`
- `components/template/v51.vue`
- `components/template/v42.vue`
- 其他版本...

**认证页面**：
- `extreme/v21/auth/index.vue`
- `extreme/v11/auth/index.vue`
- `extreme/v20/auth/index.vue`
- `extreme/v90/auth/index.vue`
- 其他版本...

#### 4.2 页面改造方式

将现有的"获取验证码"按钮替换为新的短信验证码组件：

```vue
<!-- 原来的代码 -->
<button @click="clickGetCode" :disabled="codeCountdown > 0">
  {{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
</button>

<!-- 替换为 -->
<sms-captcha 
  :phone="form.phone"
  :channel-id="form.channelId"
  @success="handleSmsSuccess"
  @error="handleSmsError"
/>
```

### 5. 实施步骤

#### 第一阶段：基础设施搭建（1-2天）
1. ✅ 修改index.html引入SDK
2. ✅ 创建验证码配置文件
3. ✅ 开发基础验证码组件
4. ✅ 添加验证码相关API接口

#### 第二阶段：核心功能开发（2-3天）
1. ✅ 开发短信验证码组件
2. ✅ 改造短信发送API接口
3. ✅ 在1-2个版本中进行集成测试

#### 第三阶段：全面部署（3-5天）
1. ✅ 在所有需要的页面中集成新组件
2. ✅ 功能测试和兼容性验证
3. ✅ 性能优化和用户体验调整

### 6. 技术实现要点

#### 6.1 用户交互流程
1. 用户输入手机号
2. 点击"获取验证码"按钮
3. 弹出阿里云人机验证界面
4. 用户完成滑块或点选验证
5. 验证通过后自动发送短信验证码
6. 开始60秒倒计时，按钮变为不可点击状态

#### 6.2 错误处理
- 验证码验证失败：显示友好提示，允许重试
- 短信发送失败：显示具体错误信息
- 网络异常：提供重试机制

#### 6.3 样式适配
- 确保验证码弹窗在移动端H5上显示正常
- 保持与现有UI风格一致
- 适配不同屏幕尺寸

#### 6.4 配置管理
- 支持不同环境使用不同的验证码场景ID
- 支持验证码样式自定义
- 支持开关控制是否启用验证码

### 7. 文件清单

#### 新建文件
- `utils/captcha-config.js` - 验证码配置
- `components/captcha/aliyun-captcha.vue` - 基础验证码组件  
- `components/captcha/sms-captcha.vue` - 短信验证码组件
- `apis/captcha.js` - 验证码相关API

#### 修改文件
- `index.html` - 引入SDK
- 各版本首页模板组件 - 集成新组件
- 各版本auth页面 - 集成新组件
- `apis/common-2.js` - 扩展短信发送接口

### 8. 预期效果

实施完成后，用户在web端获取短信验证码的体验：
1. 点击"获取验证码"按钮
2. 弹出人机验证界面（滑块或点选）
3. 验证通过后自动发送短信
4. 显示"发送成功"提示
5. 按钮进入60秒倒计时状态

这个方案专注于核心需求，实现简单高效，对现有代码改动最小，能够有效防止恶意刷短信的行为。
